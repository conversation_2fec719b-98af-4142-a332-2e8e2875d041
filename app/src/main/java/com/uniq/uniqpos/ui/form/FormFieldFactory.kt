package com.uniq.uniqpos.ui.form

class FormFieldFactory {
    fun createField(config: FormFieldConfig): FormFieldComponent {
        return when(config.type) {
            FormFieldConfig.TYPE_TEXT -> TextFieldComponent(config)
            FormFieldConfig.TYPE_SINGLE_CHOICE -> SingleChoiceComponent(config)
            // FormFieldConfig.TYPE_MULTI_CHOICE -> MultiChoiceComponent(config) // Will implement later if needed
            else -> throw IllegalArgumentException("Unknown field type: ${config.type}")
        }
    }
}
