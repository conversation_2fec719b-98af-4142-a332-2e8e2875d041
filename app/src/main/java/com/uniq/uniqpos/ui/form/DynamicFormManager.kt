package com.uniq.uniqpos.ui.form

import android.widget.LinearLayout
import timber.log.Timber

class DynamicFormManager(
    private val container: LinearLayout,
    private val fieldFactory: FormFieldFactory = FormFieldFactory()
) {
    private val formFields = mutableListOf<FormFieldComponent>()
    
    fun buildForm(configurations: List<FormFieldConfig>) {
        // Clear previous form
        container.removeAllViews()
        formFields.clear()
        
        // Sort by display order and create fields
        configurations
            .sortedBy { it.displayOrder }
            .forEach { config ->
                try {
                    val field = fieldFactory.createField(config)
                    val view = field.createView(container.context)
                    
                    container.addView(view)
                    formFields.add(field)
                    Timber.i("transactionSetting, field added: ${config.label} (${config.type})")
                } catch (e: Exception) {
                    // Log error but don't crash - skip this field
                    e.printStackTrace()
                }
            }
    }
    
    fun getFormData(): Map<String, Any?> {
        return formFields.associate { field ->
            field.config.id to field.getValue()
        }
    }
    
    fun validateForm(): ValidationResult {
        formFields.forEach { field ->
            val result = field.validate()
            if (result is ValidationResult.Error) {
                return result
            }
        }
        return ValidationResult.Success
    }
    
    fun resetForm() {
        formFields.forEach { it.reset() }
    }
    
    fun hasFields(): Boolean = formFields.isNotEmpty()
}
