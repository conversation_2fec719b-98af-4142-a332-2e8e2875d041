package com.uniq.uniqpos.data.local.migration

import androidx.room.migration.Migration
import androidx.sqlite.db.SupportSQLiteDatabase

/**
 * Migration from database version 32 to 33.
 * Adds the custom_fields column to the sales table for storing transaction custom fields.
 */
fun MIGRATION_32_33() = object : Migration(32, 33) {
    override fun migrate(database: SupportSQLiteDatabase) {
        // Add custom_fields column to sales table
        database.execSQL("ALTER TABLE sales ADD COLUMN custom_fields TEXT")
    }
}
