package com.uniq.uniqpos.data.local.converter

import androidx.room.TypeConverter
import com.google.gson.Gson
import com.google.gson.reflect.TypeToken
import com.uniq.uniqpos.model.CustomFieldData

class CustomFieldDataConverter {
    @TypeConverter
    fun fromCustomFieldDataList(value: ArrayList<CustomFieldData>?): String? {
        return if (value == null) null else Gson().toJson(value)
    }

    @TypeConverter
    fun toCustomFieldDataList(value: String?): ArrayList<CustomFieldData>? {
        return if (value == null) null else {
            val listType = object : TypeToken<ArrayList<CustomFieldData>>() {}.type
            Gson().fromJson(value, listType)
        }
    }
}
