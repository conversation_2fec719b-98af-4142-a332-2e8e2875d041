package com.uniq.uniqpos.data.local.migration

import androidx.room.migration.Migration
import androidx.sqlite.db.SupportSQLiteDatabase

/**
 * Migration from database version 31 to 32.
 * Makes the option_values column in transaction_setting table nullable.
 */
fun MIGRATION_31_32() = object : Migration(31, 32) {
    override fun migrate(database: SupportSQLiteDatabase) {
        // Create a temporary table with the new schema
        database.execSQL("""
            CREATE TABLE transaction_setting_temp (
                id INTEGER NOT NULL PRIMARY KEY,
                outlet_id INTEGER NOT NULL,
                label TEXT NOT NULL,
                type TEXT NOT NULL,
                option_values TEXT,
                is_required INTEGER NOT NULL,
                display_order INTEGER NOT NULL,
                created_at INTEGER NOT NULL,
                updated_at INTEGER NOT NULL,
                synced INTEGER NOT NULL DEFAULT 1
            )
        """)

        // Copy data from the old table to the temporary table
        database.execSQL("""
            INSERT INTO transaction_setting_temp 
            SELECT id, outlet_id, label, type, option_values, is_required, display_order, created_at, updated_at, synced 
            FROM transaction_setting
        """)

        // Drop the old table
        database.execSQL("DROP TABLE transaction_setting")

        // Rename the temporary table to the original table name
        database.execSQL("ALTER TABLE transaction_setting_temp RENAME TO transaction_setting")
    }
}