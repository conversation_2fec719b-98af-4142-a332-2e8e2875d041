package com.uniq.uniqpos.view.transaction


import android.app.Activity
import android.app.SearchManager
import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.content.IntentFilter
import android.os.Build
import android.os.Bundle
import android.text.InputType
import android.view.*
import android.widget.SearchView
import android.widget.TextView
import androidx.activity.result.contract.ActivityResultContracts
import androidx.appcompat.app.AppCompatActivity.RECEIVER_NOT_EXPORTED
import androidx.lifecycle.lifecycleScope
import com.bugsnag.android.Bugsnag
import com.google.firebase.analytics.ktx.analytics
import com.google.firebase.ktx.Firebase
import com.google.firebase.remoteconfig.ktx.remoteConfig
import com.google.gson.Gson
import com.uniq.uniqpos.R
import com.uniq.uniqpos.data.local.entity.ProductEntity
import com.uniq.uniqpos.data.local.entity.SalesEntity
import com.uniq.uniqpos.data.local.entity.SalesTagEntity
import com.uniq.uniqpos.data.local.entity.SubCategoryEntity
import com.uniq.uniqpos.data.local.entity.TmpSalesEntity
import com.uniq.uniqpos.data.local.sharedpref.SharedPref
import com.uniq.uniqpos.data.local.sharedpref.getJson
import com.uniq.uniqpos.data.local.sharedpref.getJsonList
import com.uniq.uniqpos.data.local.sharedpref.getLocalDataBoolean
import com.uniq.uniqpos.data.local.sharedpref.getLocalDataString
import com.uniq.uniqpos.data.local.sharedpref.putData
import com.uniq.uniqpos.data.local.sharedpref.sharedPref
import com.uniq.uniqpos.data.remote.Status
import com.uniq.uniqpos.data.remote.model.PromotionUsage
import com.uniq.uniqpos.databinding.DialogPromotionUsageBinding
import com.uniq.uniqpos.databinding.FragmentTransactionMainBinding
import com.uniq.uniqpos.databinding.ListItemPromotionUsageBinding
import com.uniq.uniqpos.model.BottomDialogModel
import com.uniq.uniqpos.util.*
import com.uniq.uniqpos.util.lifecycle.setupLoadingDialog
import com.uniq.uniqpos.util.lifecycle.setupPermissionRequest
import com.uniq.uniqpos.util.lifecycle.setupSnackbar
import com.uniq.uniqpos.util.lifecycle.setupToastMessage
import com.uniq.uniqpos.util.networking.WebSocketServer
import com.uniq.uniqpos.view.cart.TransactionCartActivity
import com.uniq.uniqpos.view.global.BaseFragment
import com.uniq.uniqpos.view.global.BottomDialogInput
import com.uniq.uniqpos.view.global.DynamicDialog
import com.uniq.uniqpos.view.global.GlobalAdapter
import com.uniq.uniqpos.view.main.MainActivity
import com.uniq.uniqpos.view.selforder.SelfOrderActivity
import com.uniq.uniqpos.view.table.TableListActivity
import com.uniq.uniqpos.view.transaction.dialog.ProductDetailDialogFragment
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import timber.log.Timber
import java.lang.ref.WeakReference

class TransactionMainFragment :
    BaseFragment<TransactionViewModel, FragmentTransactionMainBinding>(),
    SearchView.OnQueryTextListener {

    private val RC_CART = 21
    private val RC_SELF_ORDER = 22

    private val TAG_TRANSACTION_FRAGMENT = "transaction_fragment"
    private lateinit var searchView: SearchView
    val transactionFragment = TransactionFragment()
    private val chooseMenuFragment = ChooseMenuFragment()
    private var broadcastReceiver: BroadcastReceiver? = null
    private var txtCartBadge: TextView? = null
    private var cartCount = 0
    private var selfOrderDialog: BottomDialogInput? = null
    private var productDetailDialog: ProductDetailDialogFragment? = null

    private val requestPermissionLauncher = registerForActivityResult(ActivityResultContracts.RequestPermission()) { isGranted ->
        if(!isGranted){
            toast("bluetooth permission required to connect with printer!")
        }
    }

    override val layoutRes = R.layout.fragment_transaction_main
    override fun getViewModel() = TransactionViewModel::class.java
    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        isSubscribeToSharedPref = true
        return super.onCreateView(inflater, container, savedInstanceState)
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setHasOptionsMenu(true)
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        Timber.d("[LIFE] #onViewCreated, null? ${savedInstanceState == null}")
        Bugsnag.leaveBreadcrumb(this::class.simpleName.safe())
//        binding.lifecycleOwner = viewLifecycleOwner
        if (savedInstanceState == null) {
//            productDetailDialog = ProductDetailDialogFragment()
            printerSocket.setActivity(activity)
//            productDetailDialog?.setViewModel(viewModel)
            try {
                broadcastReceiver = object : BroadcastReceiver() {
                    override fun onReceive(context: Context?, intent: Intent?) {
                        when (intent?.getStringExtra("sync")) {
                            "order_sales" -> viewModel.syncOrderSales(context?.outlet()?.outletId)
                        }
                    }
                }

                WeakReference<Context>(context).get()?.apply {
                    if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU){
                        registerReceiver(broadcastReceiver, IntentFilter(Constant.INTENT_SYNC_REQUEST), RECEIVER_NOT_EXPORTED)
                    }else{
                        registerReceiver(broadcastReceiver, IntentFilter(Constant.INTENT_SYNC_REQUEST))
                    }
                }

                val selectedFragment =
                    view.findViewById<TextView>(R.id.txt_wide_screen)?.let { transactionFragment }
                        ?: kotlin.run { chooseMenuFragment }
                childFragmentManager.beginTransaction()
                    .replace(R.id.transaction_container, selectedFragment, TAG_TRANSACTION_FRAGMENT)
                    .commit()
            } catch (e: Exception) {
                Timber.i("registering broadcast receiver error : $e")
            }

            if (context?.getLocalDataBoolean(SharedPref.PRINTER_SERVER_STATUS) == true) {
                printerSocket.start()
            }
//            printerSocket.startKitchenServer()
            lifecycleScope.launch(Dispatchers.IO) {
//                WebSocketServer().run(context?.assets!!)
            }
        }

        val barcodeListener = object : TextView.OnEditorActionListener {
            override fun onEditorAction(v: TextView?, actionId: Int, event: KeyEvent?): Boolean {
                event?.let { keyEvent ->
                    if (keyEvent.action == KeyEvent.ACTION_DOWN && !binding.edtBarcode.isEmptyValue()) {
                        val barcode = binding.edtBarcode.text.toString().trim()
                        Timber.i("Barcode '$barcode'")
                        viewModel.barcodeScan.postValue(barcode)
                        viewModel.checkBarcodeProduct(barcode)
                        binding.edtBarcode.setText("")
                        return true
                    }
                }
                return false
            }
        }

        binding.edtBarcode.setOnEditorActionListener(barcodeListener)

        view.setupSnackbar(this, viewModel.snackbarText)
        context?.setupToastMessage(this, viewModel.toastMessage)

        requireContext().setupLoadingDialog(getBaseFragment(), this, viewModel.loadingDialog)

        //permissions
        requireContext().setupPermissionRequest(this, viewModel.requestPermission, requestPermissionLauncher, activity)

        observeData()
        observeTask()
        checkLastSync()
    }

    private fun observeData() {
        viewModel.loadNotesHistory()
        viewModel.loadDiscAndVoucherInfoHistory()
        viewModel.loadPrinter()

        val outlet = context?.outlet()
        viewModel.getGratuity()
            .observe(viewLifecycleOwner) { items ->
                viewModel.gratuities.clear()
                items?.data?.let { viewModel.gratuities.addAll(it) }
                viewModel.initDefaultTax()
            }

        viewModel.getTax(outlet?.outletId.safe())
            .observe(viewLifecycleOwner) { items ->
                viewModel.taxes.clear()
                items?.data?.forEach { viewModel.taxes.add(it) }
            }

        viewModel.loadMultiplePrice(outlet?.outletId.safe())
            .observe(viewLifecycleOwner) { items ->
                viewModel.multiplePriceList.clear()
                items?.data?.let { viewModel.multiplePriceList.addAll(it) }
            }

        viewModel.getProducts(outlet?.outletId.safe())
            .observe(viewLifecycleOwner) {
                if (it.status != Status.LOADING) {
                    viewModel.taskShowTutorialProduct.postValue(it.data?.isEmpty())
                }

                viewModel.products.clear()
                Timber.i(">>PRODUCT observer, ${it.data?.size}")
                it?.data?.let {
                    viewModel.products.addAll(it)
                }
                viewModel.filterCategory()
                viewModel.refreshProduct.call()
            }

        viewModel.getProductVariant().observe(viewLifecycleOwner) { resp ->
            viewModel.variants.clear()
            resp?.data?.let { viewModel.variants.addAll(it) }
        }

        viewModel.getCategories()
            .observe(viewLifecycleOwner) { resource ->
                resource?.data?.takeIf { it.isNotEmpty() }?.let { subcategories ->
//                    viewModel.refreshSubCategories(subcategories)
                    viewModel.categoriesTmp.clear()

                    //adjust position
                    try {
                        val savedSubcategories = ArrayList<SubCategoryEntity>()
                        context?.getLocalDataString(SharedPref.SUBCATEGORY_DATA)?.takeIf { it.isNotBlank() }?.let { subcategoryData ->
                            val saved: ArrayList<SubCategoryEntity> = Gson().fromJson(subcategoryData)
                            savedSubcategories.addAll(saved)
                        }
                        subcategories.forEach { subCategoryEntity ->
                            subCategoryEntity.position = savedSubcategories.firstOrNull{ it.productCategoryId == subCategoryEntity.productCategoryId }?.position.safe()
                            Timber.d("${subCategoryEntity.position}. ${subCategoryEntity.name}")
                        }
                    } catch (e: Exception) {
                        Timber.i("err load subcategory saved: $e")
                    }

                    //adding promo & all tab
                    mapOf( Constant.MenuCategoryPromo to getString(R.string.promotion),
                        Constant.MenuCategoryAll to getString(R.string.all_categories)
                    ).forEach { (id, name) ->
                        viewModel.categoriesTmp.add(SubCategoryEntity(id, name, position = id))
                    }

                    viewModel.categoriesTmp.addAll(subcategories)
                    viewModel.filterCategory()
                    viewModel.refreshCategory.call()
                }
            }

        viewModel.getTransactionCartCount()
            .observe(viewLifecycleOwner) {
                viewModel.refreshTransactionCartCount.postValue(it ?: 0)
                updateCartCount(it.safe())
            }

        viewModel.getPendingPrintCount()
            .observe(viewLifecycleOwner) {
                viewModel.refreshPendingPrintCount.postValue(it ?: 0)
            }

        viewModel.getLinkMenuLive(context?.outlet()?.outletId ?: 0)
            .observe(viewLifecycleOwner) { items ->
                viewModel.linkMenuList.clear()
                items?.data?.let { viewModel.linkMenuList.addAll(it) }
            }

        viewModel.getPromotionLive(outlet?.outletId ?: 0)
            .observe(viewLifecycleOwner) { items ->
                items?.data?.let { data ->
                    viewModel.updatePromotionList(data)
                }
            }

        viewModel.getSalesTagLive().observe(this){ salesTagList ->
            viewModel.salesTagList.clear()
            viewModel.salesTagList.addAll(salesTagList)
            //adding add button
            if(salesTagList.isNotEmpty())
            viewModel.salesTagList.add(SalesTagEntity(0,0,"", 0,0,"+Add"))
        }

        viewModel.vmKitchen.loadKitchen().observe(viewLifecycleOwner){
            it.data?.let { kitchen -> viewModel.vmKitchen.updateKitchenList(kitchen) }
        }

        viewModel.getTransactionSettingLive().observe(viewLifecycleOwner){
            Timber.i("transactionSetting: ${Gson().toJson(it)}")
            viewModel.transactionSettingList.clear()
            viewModel.transactionSettingList.addAll(it)
        }

        val remoteConfig = Firebase.remoteConfig
//        remoteConfig.addOnConfigUpdateListener(object : ConfigUpdateListener{
//            override fun onUpdate(configUpdate: ConfigUpdate) {
//                remoteConfig.fetchAndActivate().addOnCompleteListener { }
//            }
//
//            override fun onError(error: FirebaseRemoteConfigException) {
//                Bugsnag.notify(error)
//                Timber.i("fetch firebase remote conf err: $error")
//            }
//        })
    }

    private fun updateCartCount(count: Int) {
        txtCartBadge?.setVisible(count > 0)
        txtCartBadge?.text = "$count"
        cartCount = count
    }

    private fun observeTask() {
        viewModel.pDialogTask.observe(viewLifecycleOwner) { message ->
            showDialog(!isProgressDialogShowing(), message ?: "Loading...")
        }

        viewModel.printTask.observe(viewLifecycleOwner) { data ->
            data.first.let { printData ->
                Timber.i("Printing... size to print ${printData.size}")
                lifecycleScope.launch(Dispatchers.Main) {
                    managePrintWifi(
                        printData
                    ) { isConnected, _ -> //if failed to send to server, then save to pending
                        if (!isConnected) {
                            Timber.i("save to pending : ${Gson().toJson(printData)}")
                            viewModel.savePendingPrint(printData)
                        }
                    }
                }
            }
        }

        viewModel.dialogMessageTask.observe(viewLifecycleOwner) { msg ->
            showMsg(msg)
        }

        viewModel.taskShowMessage.observe(viewLifecycleOwner) { msg ->
            showDialog(false)
            if (msg is Exception) {
                context?.showMessage(msg.readableError(context))
            }
        }

        viewModel.taskSendToLocalServer.observe(viewLifecycleOwner) { salesCart ->
            salesCart?.let { sendSalesToLocalServer(it) }
        }

        viewModel.taskPromotionUsageDialog.observe(viewLifecycleOwner){ promotionUsages ->
            promotionUsages?.let { showPromotionUsageDialog(it) }
        }
    }

    private fun showPromotionUsageDialog(promotionUsages: ArrayList<PromotionUsage>) {
        val viewBinding = DialogPromotionUsageBinding.inflate(layoutInflater)
        val promoDialog = DynamicDialog(viewBinding.root, requireContext())

        viewBinding.imgClose.setOnClickListener {
            promoDialog.dismiss()
        }
        viewBinding.recviewUsage.adapter = object: GlobalAdapter<ListItemPromotionUsageBinding>(R.layout.list_item_promotion_usage, promotionUsages){}

        promoDialog.show()
    }

    fun showProductDetail(product: ProductEntity) {
//        if(productDetailDialog == null) {
//            productDetailDialog = ProductDetailDialogFragment()
//            productDetailDialog?.setViewModel(viewModel)
//        }
        val productDetailDialog = ProductDetailDialogFragment()
        productDetailDialog.setViewModel(viewModel)

        productDetailDialog.setProduct(product)
        productDetailDialog.show(childFragmentManager, "product-${product.productId}")

        Firebase.analytics
            .logEvent(
                "app_features",
                bundleOf(
                    "Outlet" to "SPD:${context.outlet()?.outletId}:${context.outlet()?.name}",
                    "Feature" to "Show Product Detail"
                )
            )
    }

    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)
        Timber.i("onActivityResult...")
        if (resultCode == Activity.RESULT_OK) {
            if (requestCode == RC_CART) {
                val sales = data?.extras?.getParcelable<SalesEntity>("sales")
                val isPayment = data?.extras?.getSerializable("isPayment") as? Boolean
                if (sales is SalesEntity) {
                    if (isPayment == true) {
                        printSales(sales)
                        viewModel.restorePromotion()
                        viewModel.syncSales(sales)
                    } else {
                        Timber.i(">> Sales from cart : ${Gson().toJson(sales)}")
                        val mergeIds =
                            data.extras?.getSerializable("merge_ids") as? ArrayList<String>
                        Timber.i("merge ids : $mergeIds")
                        viewModel.initSalesData(sales, mergeIds)
                    }
                }
            } else if (requestCode == RC_SELF_ORDER) {
                val code = data?.getStringExtra("code")
                viewModel.findSelfOrder(code.safe(), outlet.outletId.safe())
                Timber.i("self-order-code: $code")
            }
        }
        checkLastSync()
    }

    fun navigateToCart() {
        (activity as MainActivity).getTransactionMain()
        startActivityForResult(Intent(context, TransactionCartActivity::class.java), RC_CART)
    }

    fun navigateToTableList() {
        startActivityForResult(Intent(context, TableListActivity::class.java), RC_CART)
    }

    fun refreshViewAfterSynced() {
        if (isAdded) {
            val fragment = childFragmentManager.findFragmentByTag(TAG_TRANSACTION_FRAGMENT)
            if (fragment is TransactionFragment) {
                fragment.initVisibilityViewFinishOrder()
            } else if (fragment is ChooseMenuFragment) {

            }
        }
    }

    fun printSales(sales: SalesEntity, isOnlyPrintKitchen: Boolean = false) {
        viewModel.generatePrintFormat(
            sales,
            context?.outlet()!!,
            context?.employee()!!,
            isOnlyPrintKitchen,
            false,
            true
        )

        viewModel.vmKitchen.sendToKitchen(sales)
    }

    fun sendSalesCartToLocalServer(salesCart: TmpSalesEntity) {
//        sendSalesToLocalServer(salesCart)
        viewModel.sendCartToLocalServer(salesCart.noNota)
    }

    private fun showSelfOrderDialog() {
        if (selfOrderDialog == null) {
            selfOrderDialog = BottomDialogInput()
            selfOrderDialog?.setOnButtonClick { code ->
                viewModel.findSelfOrder(code, context?.outlet()?.outletId)
            }
            selfOrderDialog?.setUseBarcode(true)
            selfOrderDialog?.setModel(
                BottomDialogModel(
                    "SELF ORDER",
                    "CHECK",
                    "order code",
                    hint = "type order code",
                    inputType = InputType.TYPE_CLASS_NUMBER
                )
            )
        }

//        selfOrderDialog?.dismiss()
        selfOrderDialog?.show(childFragmentManager, "self-order")
    }

    fun updateSelfOrderDialogUi(err: String? = null) {
        err?.let { selfOrderDialog?.setError(err) } ?: run { selfOrderDialog?.dismiss() }
    }

    override fun onQueryTextSubmit(query: String?): Boolean {
        return true
    }

    override fun onQueryTextChange(newText: String?): Boolean {
        viewModel.searchProduct.postValue(newText)
        return true
    }

    override fun onPrepareOptionsMenu(menu: Menu) {
        super.onPrepareOptionsMenu(menu)
        Timber.d("[LIFE] #onPrepareOptionsMenu")
    }

    override fun onCreateOptionsMenu(menu: Menu, inflater: MenuInflater) {
        inflater.inflate(R.menu.menu_main_transaction, menu)
        val searchManager = context?.getSystemService(Context.SEARCH_SERVICE) as SearchManager
        searchView = menu.findItem(R.id.action_search)?.actionView as SearchView
        searchView.setSearchableInfo(searchManager.getSearchableInfo(activity?.componentName))
        searchView.setOnQueryTextListener(this)

        val menuItemCart: MenuItem? = menu.findItem(R.id.action_cart)
        val actionView = menuItemCart?.actionView
        txtCartBadge = actionView?.findViewById(R.id.cart_badge)
        actionView?.setOnClickListener { onOptionsItemSelected(menuItemCart) }
        menuItemCart?.isVisible = context?.outletFeature()?.notelist ?: true
        updateCartCount(cartCount)

        Timber.d("[LIFE] #onCreateOptionsMenu")
        super.onCreateOptionsMenu(menu, inflater)
    }

    override fun onOptionsItemSelected(item: MenuItem): Boolean {
        when (item.itemId) {
            R.id.action_cart -> navigateToCart()
            R.id.action_self_order -> showSelfOrderDialog()
            R.id.action_self_order_list -> startActivityForResult(
                Intent(
                    requireContext(),
                    SelfOrderActivity::class.java
                ), RC_SELF_ORDER
            )
        }
        return super.onOptionsItemSelected(item)
    }

    private fun checkLastSync(){
        context?.sharedPref()?.getString(SharedPref.LAST_SYNC)?.let { lastSync ->
            //check if last sync more than 5 minutes
            val diffMinutes = lastSync.toLong().diffMinute()
            if (diffMinutes > 5) {
                val diffHour = diffMinutes / 60
                val diffHourMinute = diffMinutes % 60
                val diffInfo = if (diffHour > 0) "$diffHour jam, $diffHourMinute menit" else "$diffMinutes menit"
                context?.putData(SharedPref.WARNING_APP, "singkronisasi data terakhir $diffInfo yang lalu")
            }else{
                context?.putData(SharedPref.WARNING_APP, "")
            }
            showLastSyncWarning()
            Timber.i("last sync : $lastSync | diff : $diffMinutes")
        }
    }

    override fun onSharedPrefChanged(key: String) {
        Timber.i("shared pref changed : $key")
        if(key == SharedPref.WARNING_APP){
            showLastSyncWarning()
        }else if (key == SharedPref.LAST_SYNC){
            checkLastSync()
        }
    }

    private fun showLastSyncWarning(){
        val warning = context?.sharedPref()?.getString(SharedPref.WARNING_APP)
        _binding?.layoutWarning?.text = warning
        _binding?.layoutWarning?.setVisible(warning.safe().isNotBlank())
        Timber.i("warning change.. $warning")
    }

    override fun onDestroy() {
        Timber.d("[LIFE] #onDestroy")
//        binding!!.edtBarcode.setOnEditorActionListener(null)
        broadcastReceiver?.let { context?.unregisterReceiver(it) }
        printerSocket.removeActivityBound()
        productDetailDialog = null
//        productDetailDialog = null
        super.onDestroy()
    }
}
