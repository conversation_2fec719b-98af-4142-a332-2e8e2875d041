package com.uniq.uniqpos.model

import android.os.Parcelable
import com.google.gson.annotations.SerializedName
import kotlinx.android.parcel.Parcelize

@Parcelize
data class CustomFieldData(
    @SerializedName("setting_transaction_fkid")
    val settingTransactionFkid: Int,
    
    @SerializedName("label")
    val label: String,
    
    @SerializedName("value")
    val value: String
) : Parcelable
