package com.uniq.uniqpos.data.remote.repository

import com.uniq.uniqpos.data.local.entity.TransactionSettingEntity
import org.junit.Test
import org.junit.Assert.*

/**
 * Unit test for SettingRepository transaction settings functionality
 */
class SettingRepositoryTest {

    @Test
    fun transactionSettingEntity_creation_isCorrect() {
        // Test data based on the example response
        val transactionSetting = TransactionSettingEntity(
            id = 1,
            outletId = 29,
            label = "Status Member",
            type = "SINGLE_CHOICE",
            optionValues = "[\"Member Baru\", \"Member Lama\"]",
            isRequired = false,
            displayOrder = 0,
            createdAt = 1749788269530,
            updatedAt = 1749788269530,
            synced = true
        )

        // Verify the entity is created correctly
        assertEquals(1, transactionSetting.id)
        assertEquals(29, transactionSetting.outletId)
        assertEquals("Status Member", transactionSetting.label)
        assertEquals("SINGLE_CHOICE", transactionSetting.type)
        assertEquals("[\"Member Baru\", \"Member Lama\"]", transactionSetting.optionValues)
        assertEquals(false, transactionSetting.isRequired)
        assertEquals(0, transactionSetting.displayOrder)
        assertEquals(1749788269530, transactionSetting.createdAt)
        assertEquals(1749788269530, transactionSetting.updatedAt)
        assertEquals(true, transactionSetting.synced)
    }

    @Test
    fun transactionSettingEntity_defaultSyncedValue_isTrue() {
        val transactionSetting = TransactionSettingEntity(
            id = 1,
            outletId = 29,
            label = "Test Setting",
            type = "SINGLE_CHOICE",
            optionValues = "[]",
            isRequired = false,
            displayOrder = 0,
            createdAt = System.currentTimeMillis(),
            updatedAt = System.currentTimeMillis()
        )

        // Verify default synced value is true
        assertEquals(true, transactionSetting.synced)
    }
}
